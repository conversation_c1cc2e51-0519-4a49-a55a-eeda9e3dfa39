import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_database/firebase_database.dart';
import '../models/chat_models.dart';
import '../services/chat_service.dart';
import '../services/auth_service.dart';
import '../services/otp_service.dart';
import '../services/report_service.dart';
import '../themes/app_theme.dart';
import 'item_detail_screen.dart';

class ChatScreen extends StatefulWidget {
  final ChatRoom chatRoom;

  const ChatScreen({
    super.key,
    required this.chatRoom,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  ChatParticipant? _otherParticipant;
  Map<String, dynamic>? _targetItem;
  Map<String, dynamic>? _offeredItem;
  bool _isLoading = true;
  String? _swapRequestStatus;
  bool _isProcessingRequest = false;

  @override
  void initState() {
    super.initState();
    _loadChatData();
    _markMessagesAsRead();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadChatData() async {
    try {
      final currentUser = AuthService.currentUser;
      if (currentUser == null) return;

      // Get other participant data
      final otherParticipantId = widget.chatRoom.participants
          .firstWhere((id) => id != currentUser.uid);
      _otherParticipant = await ChatService.getUserData(otherParticipantId);

      // Get item data
      if (widget.chatRoom.targetItemId != null) {
        _targetItem = await ChatService.getItemData(widget.chatRoom.targetItemId!);
      }
      if (widget.chatRoom.offeredItemId != null) {
        _offeredItem = await ChatService.getItemData(widget.chatRoom.offeredItemId!);
      }

      // Get swap request status
      if (widget.chatRoom.swapRequestId != null) {
        _swapRequestStatus = await _getSwapRequestStatus(widget.chatRoom.swapRequestId!);
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _markMessagesAsRead() async {
    await ChatService.markMessagesAsRead(widget.chatRoom.chatId);
  }

  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    final currentUser = AuthService.currentUser;
    if (currentUser == null) return;

    _messageController.clear();

    final success = await ChatService.sendMessage(
      chatId: widget.chatRoom.chatId,
      senderId: currentUser.uid,
      message: message,
      type: MessageType.text,
    );

    if (success) {
      _scrollToBottom();
    } else {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to send message'),
            backgroundColor: AppTheme.getErrorColor(context),
          ),
        );
      }
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingView() : _buildChatView(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppTheme.getBackgroundColor(context),
      elevation: 0,
      titleSpacing: 1,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: Icon(
          Icons.arrow_back,
          color: AppTheme.getTextColor(context),
        ),
      ),
      title: Row(
        children: [
          // Profile picture
          _otherParticipant == null
              ? Shimmer.fromColors(
                  baseColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                  highlightColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.1),
                  child: CircleAvatar(
                    radius: 20,
                    backgroundColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                  ),
                )
              : CircleAvatar(
                  radius: 20,
                  backgroundColor: AppTheme.getPrimaryColor(context),
                  backgroundImage: _otherParticipant?.profileImageUrl != null
                      ? CachedNetworkImageProvider(_otherParticipant!.profileImageUrl!)
                      : null,
                  child: _otherParticipant?.profileImageUrl == null
                      ? Text(
                          _otherParticipant?.name.isNotEmpty == true
                              ? _otherParticipant!.name[0].toUpperCase()
                              : '?',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      : null,
                ),
          const SizedBox(width: 12),

          // Name and status
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _otherParticipant == null
                    ? Shimmer.fromColors(
                        baseColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                        highlightColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.1),
                        child: Container(
                          height: 16,
                          width: 120,
                          decoration: BoxDecoration(
                            color: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      )
                    : Text(
                        _otherParticipant!.name,
                        style: TextStyle(
                          color: AppTheme.getTextColor(context),
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                const SizedBox(height: 4),
                Text(
                  widget.chatRoom.requestType == 'swap' ? 'Swap Request' : 'Buy Request',
                  style: TextStyle(
                    color: AppTheme.getSecondaryTextColor(context),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          onPressed: () => _showReportModal(),
          icon: Icon(
            Icons.report_outlined,
            color: AppTheme.getTextColor(context),
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildChatView() {
    return Column(
      children: [
        // Item info header
        if (_targetItem != null || _offeredItem != null) _buildItemHeader(),

        // Messages
        Expanded(
          child: StreamBuilder<List<ChatMessage>>(
            stream: ChatService.getChatMessages(widget.chatRoom.chatId),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (snapshot.hasError) {
                return Center(
                  child: Text(
                    'Error loading messages',
                    style: TextStyle(
                      color: AppTheme.getErrorColor(context),
                      fontSize: 16,
                    ),
                  ),
                );
              }

              final messages = snapshot.data ?? [];
              if (messages.isEmpty) {
                return Center(
                  child: Text(
                    'Start the conversation!',
                    style: TextStyle(
                      color: AppTheme.getSecondaryTextColor(context),
                      fontSize: 16,
                    ),
                  ),
                );
              }

              WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());

              return ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                itemCount: messages.length,
                itemBuilder: (context, index) {
                  return _buildMessageBubble(messages[index]);
                },
              );
            },
          ),
        ),

        // Message input or closed message
        _buildMessageInputArea(),
      ],
    );
  }

  Widget _buildItemHeader() {
    final currentUser = AuthService.currentUser;
    if (currentUser == null) return const SizedBox.shrink();

    // Determine which item belongs to current user and which to other user
    Map<String, dynamic>? myItem;
    Map<String, dynamic>? otherItem;

    // Check if current user is the owner of target item or requester
    final isOwner = _targetItem?['userId'] == currentUser.uid;

    if (isOwner) {
      // Current user is owner, so target item is theirs, offered item is other's
      myItem = _targetItem;
      otherItem = _offeredItem;
    } else {
      // Current user is requester, so offered item is theirs, target item is other's
      myItem = _offeredItem;
      otherItem = _targetItem;
    }

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          // Items row
          Row(
            children: [
              // My item (left side)
              Expanded(
                child: _buildItemCard(
                  item: myItem,
                  label: 'Your Item',
                  isClickable: false,
                ),
              ),

              // Arrows in center
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    Icon(
                      Icons.arrow_forward,
                      color: AppTheme.getPrimaryColor(context),
                      size: 20,
                    ),
                    const SizedBox(height: 4),
                    Icon(
                      Icons.arrow_back,
                      color: AppTheme.getPrimaryColor(context),
                      size: 20,
                    ),
                  ],
                ),
              ),

              // Other user's item (right side)
              Expanded(
                child: _buildItemCard(
                  item: otherItem,
                  label: 'Their Item',
                  isClickable: true,
                ),
              ),
            ],
          ),

          // Accept/Reject buttons (only for owner and if request is pending)
          if (_shouldShowActionButtons()) ...[
            const SizedBox(height: 16),
            _buildActionButtons(),
          ],
        ],
      ),
    );
  }

  Widget _buildItemCard({
    required Map<String, dynamic>? item,
    required String label,
    required bool isClickable,
  }) {
    if (item == null) {
      return Container(
        height: 100,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: AppTheme.getBackgroundColor(context),
          border: Border.all(
            color: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
            style: BorderStyle.solid,
          ),
        ),
        child: Center(
          child: Text(
            widget.chatRoom.requestType == 'swap' ? 'No Item' : 'Cash Offer',
            style: TextStyle(
              color: AppTheme.getSecondaryTextColor(context),
              fontSize: 12,
            ),
          ),
        ),
      );
    }

    final images = List<String>.from(item['images'] ?? []);
    final imageUrl = images.isNotEmpty ? images[0] : null;

    Widget itemWidget = Container(
      height: 100,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: AppTheme.getBackgroundColor(context),
        border: Border.all(
          color: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.2),
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: imageUrl != null
            ? CachedNetworkImage(
                imageUrl: imageUrl,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                placeholder: (context, url) => Container(
                  color: AppTheme.getBackgroundColor(context),
                  child: Icon(
                    Icons.image,
                    color: AppTheme.getSecondaryTextColor(context),
                    size: 24,
                  ),
                ),
                errorWidget: (context, url, error) => Icon(
                  Icons.image_not_supported,
                  color: AppTheme.getSecondaryTextColor(context),
                  size: 24,
                ),
              )
            : Icon(
                Icons.image_not_supported,
                color: AppTheme.getSecondaryTextColor(context),
                size: 24,
              ),
      ),
    );

    if (isClickable) {
      return GestureDetector(
        onTap: () => _navigateToItemDetail(item),
        child: itemWidget,
      );
    }

    return itemWidget;
  }

  void _navigateToItemDetail(Map<String, dynamic> item) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ItemDetailScreen(
          itemId: item['docId'] ?? item['itemId'] ?? '',
          itemData: item,
        ),
      ),
    );
  }

  bool _shouldShowActionButtons() {
    final currentUser = AuthService.currentUser;
    if (currentUser == null || _targetItem == null) return false;

    // Only show to the owner of the target item
    final isOwner = _targetItem!['userId'] == currentUser.uid;

    // Only show if request is pending (not accepted or rejected)
    final isPending = _swapRequestStatus == null || _swapRequestStatus == 'pending';

    return isOwner && isPending;
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        // Reject button
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(right: 8),
            child: ElevatedButton(
              onPressed: _isProcessingRequest
                  ? null
                  : () => _updateSwapRequestStatus('rejected'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.getErrorColor(context).withValues(alpha: 0.7),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
                elevation: 2,
              ),
              child: _isProcessingRequest
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.close, size: 18),
                        const SizedBox(width: 8),
                        Text(
                          'Reject',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ),

        // Accept button
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(left: 8),
            child: ElevatedButton(
              onPressed: _isProcessingRequest
                  ? null
                  : () => _updateSwapRequestStatus('accepted'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.getPrimaryColor(context).withValues(alpha: 0.7),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
                elevation: 2,
              ),
              child: _isProcessingRequest
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.check, size: 18),
                        const SizedBox(width: 8),
                        Text(
                          'Accept',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ),
      ],
    );
  }

  Future<String?> _getSwapRequestStatus(String swapRequestId) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('swapRequests')
          .doc(swapRequestId)
          .get();

      if (doc.exists) {
        return doc.data()?['status'] as String?;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> _updateSwapRequestStatus(String status) async {
    if (widget.chatRoom.swapRequestId == null || _isProcessingRequest) return;

    setState(() {
      _isProcessingRequest = true;
    });

    try {
      // Update swap request status in Firestore
      await FirebaseFirestore.instance
          .collection('swapRequests')
          .doc(widget.chatRoom.swapRequestId!)
          .update({'status': status});

      // Send system message to inform both users
      final currentUser = AuthService.currentUser;
      if (currentUser != null) {
        if (status == 'accepted') {
          // Send acceptance message
          await ChatService.sendMessage(
            chatId: widget.chatRoom.chatId,
            senderId: 'system',
            message: 'Swap request has been accepted! 🎉',
            type: MessageType.system,
          );

          // Send 24-hour warning message
          await ChatService.sendMessage(
            chatId: widget.chatRoom.chatId,
            senderId: 'system',
            message: 'This chat will be closed in 24 hours from now, exchange all the important details before it gets late.',
            type: MessageType.system,
          );

          // Schedule chat closure after 24 hours
          _scheduleChatClosure();

          // Hide both items from listings
          await _hideSwappedItems();
        } else {
          // Send rejection message
          await ChatService.sendMessage(
            chatId: widget.chatRoom.chatId,
            senderId: 'system',
            message: 'Swap request has been rejected.',
            type: MessageType.system,
          );

          // Close chat immediately for rejection
          await _closeChatForRejection();
        }
      }

      // Send email notifications
      await _sendEmailNotifications(status);

      // Update local state
      if (mounted) {
        setState(() {
          _swapRequestStatus = status;
          _isProcessingRequest = false;
        });
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              status == 'accepted'
                  ? 'Swap request accepted!'
                  : 'Swap request rejected',
            ),
            backgroundColor: status == 'accepted'
                ? Colors.green
                : Colors.orange,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isProcessingRequest = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to update request status'),
            backgroundColor: AppTheme.getErrorColor(context),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    }
  }

  void _scheduleChatClosure() {
    // Schedule chat closure after 24 hours
    // In a real app, this would be handled by a backend service
    // For now, we'll just update the chat with a closure timestamp
    final closureTime = DateTime.now().add(const Duration(hours: 24));

    // Update chat with closure time
    FirebaseDatabase.instance
        .ref('chats/${widget.chatRoom.chatId}')
        .update({
      'scheduledClosure': closureTime.millisecondsSinceEpoch,
      'status': 'accepted',
    });
  }

  Future<void> _hideSwappedItems() async {
    try {
      final batch = FirebaseFirestore.instance.batch();

      // Hide target item
      if (widget.chatRoom.targetItemId != null) {
        final targetItemRef = FirebaseFirestore.instance
            .collection('items')
            .doc(widget.chatRoom.targetItemId!);
        batch.update(targetItemRef, {
          'isActive': false,
          'swappedAt': FieldValue.serverTimestamp(),
          'swapStatus': 'completed',
        });
      }

      // Hide offered item
      if (widget.chatRoom.offeredItemId != null) {
        final offeredItemRef = FirebaseFirestore.instance
            .collection('items')
            .doc(widget.chatRoom.offeredItemId!);
        batch.update(offeredItemRef, {
          'isActive': false,
          'swappedAt': FieldValue.serverTimestamp(),
          'swapStatus': 'completed',
        });
      }

      await batch.commit();
    } catch (e) {
      log('Error hiding swapped items: $e');
    }
  }

  Future<void> _closeChatForRejection() async {
    try {
      // Update chat status to closed
      await FirebaseDatabase.instance
          .ref('chats/${widget.chatRoom.chatId}')
          .update({
        'status': 'rejected',
        'closedAt': DateTime.now().millisecondsSinceEpoch,
        'isClosed': true,
      });

      // Reset swap request status in item detail screen by clearing any cached data
      // This allows the requester to make a new offer
      if (widget.chatRoom.targetItemId != null) {
        // Clear any cached swap request data for this item
        await FirebaseFirestore.instance
            .collection('swapRequests')
            .where('targetItemId', isEqualTo: widget.chatRoom.targetItemId!)
            .where('requesterId', isEqualTo: AuthService.currentUser?.uid)
            .get()
            .then((snapshot) {
          for (var doc in snapshot.docs) {
            if (doc.data()['status'] == 'rejected') {
              // Mark as cleared so user can make new request
              doc.reference.update({'cleared': true});
            }
          }
        });
      }
    } catch (e) {
      log('Error closing chat for rejection: $e');
    }
  }

  Future<void> _sendEmailNotifications(String status) async {
    try {
      // Get user data for both participants
      final currentUser = AuthService.currentUser;
      if (currentUser == null) return;

      final otherParticipantId = widget.chatRoom.participants
          .firstWhere((id) => id != currentUser.uid, orElse: () => '');

      if (otherParticipantId.isEmpty) return;

      // Get user details from Firestore
      final [ownerDoc, requesterDoc] = await Future.wait([
        FirebaseFirestore.instance.collection('users').doc(_targetItem?['userId']).get(),
        FirebaseFirestore.instance.collection('users').doc(otherParticipantId == _targetItem?['userId'] ? currentUser.uid : otherParticipantId).get(),
      ]);

      final ownerData = ownerDoc.data();
      final requesterData = requesterDoc.data();

      if (ownerData == null || requesterData == null) return;

      final ownerEmail = ownerData['email'] as String?;
      final ownerName = ownerData['displayName'] ?? ownerData['name'] ?? 'User';
      final requesterEmail = requesterData['email'] as String?;
      final requesterName = requesterData['displayName'] ?? requesterData['name'] ?? 'User';
      final itemName = _targetItem?['itemName'] ?? 'Item';
      final requesterItemName = _offeredItem?['itemName'];

      if (ownerEmail == null || requesterEmail == null) return;

      // Send appropriate emails based on status
      if (status == 'accepted') {
        await OTPService.sendSwapAcceptedEmails(
          ownerEmail: ownerEmail,
          ownerName: ownerName,
          requesterEmail: requesterEmail,
          requesterName: requesterName,
          ownerItemName: itemName,
          requesterItemName: requesterItemName,
        );
      } else if (status == 'rejected') {
        await OTPService.sendSwapRejectedEmails(
          ownerEmail: ownerEmail,
          ownerName: ownerName,
          requesterEmail: requesterEmail,
          requesterName: requesterName,
          itemName: itemName,
        );
      }
    } catch (e) {
      log('Error sending email notifications: $e');
    }
  }

  Widget _buildMessageBubble(ChatMessage message) {
    final currentUser = AuthService.currentUser;
    final isMe = message.senderId == currentUser?.uid;
    final isSystem = message.senderId == 'system';

    if (isSystem) {
      return _buildSystemMessage(message);
    }

    return Align(
      alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        decoration: BoxDecoration(
          color: isMe
              ? AppTheme.getPrimaryColor(context).withValues(alpha: 0.8)
              : AppTheme.getSurfaceColor(context),
          borderRadius: BorderRadius.circular(18),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message.message,
              style: TextStyle(
                color: isMe ? Colors.white : AppTheme.getTextColor(context),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _formatTime(message.timestamp),
              style: TextStyle(
                color: isMe
                    ? Colors.white.withValues(alpha: 0.7)
                    : AppTheme.getSecondaryTextColor(context),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemMessage(ChatMessage message) {
    final currentUser = AuthService.currentUser;
    if (currentUser == null) return const SizedBox.shrink();

    // Don't show the initial swap request system message
    if (message.type == MessageType.swapRequest &&
        (message.message.contains('wants to swap') || message.message.contains('wants to buy'))) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: AppTheme.getPrimaryColor(context),
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message.message,
              style: TextStyle(
                color: AppTheme.getTextColor(context),
                fontSize: 14,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInputArea() {
    // Check if chat is closed due to rejection
    if (_swapRequestStatus == 'rejected') {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: AppTheme.getSurfaceColor(context),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            'The chat has been closed.',
            style: TextStyle(
              color: AppTheme.getSecondaryTextColor(context),
              fontSize: 16,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    return _buildMessageInput();
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'Type a message...',
                hintStyle: TextStyle(
                  color: AppTheme.getSecondaryTextColor(context),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(
                    color: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(
                    color: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(
                    color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.6),
                    width: 1.5,
                  ),
                ),
                filled: true,
                fillColor: AppTheme.getBackgroundColor(context),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              style: TextStyle(
                color: AppTheme.getTextColor(context),
              ),
              maxLines: null,
              textCapitalization: TextCapitalization.sentences,
            ),
          ),
          const SizedBox(width: 8),

          // Send button
          Container(
            decoration: BoxDecoration(
              color: AppTheme.getPrimaryColor(context),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: _sendMessage,
              icon: const Icon(
                Icons.send,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${timestamp.day}/${timestamp.month}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _showReportModal() {
    if (_otherParticipant == null) return;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _ReportModal(
        reportedUserId: _otherParticipant!.userId,
        reportedUserName: _otherParticipant!.name,
        chatId: widget.chatRoom.chatId,
      ),
    );
  }
}

class _ReportModal extends StatefulWidget {
  final String reportedUserId;
  final String reportedUserName;
  final String chatId;

  const _ReportModal({
    required this.reportedUserId,
    required this.reportedUserName,
    required this.chatId,
  });

  @override
  State<_ReportModal> createState() => _ReportModalState();
}

class _ReportModalState extends State<_ReportModal> {
  bool _isStep1 = true;
  bool _isLoading = false;
  String? _selectedReason;
  final TextEditingController _additionalInfoController = TextEditingController();

  final List<String> _reportReasons = [
    'Inappropriate behavior',
    'Spam or scam',
    'Harassment',
    'Fake profile',
    'Inappropriate content',
    'Other',
  ];

  @override
  void dispose() {
    _additionalInfoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: BoxDecoration(
        color: AppTheme.getBackgroundColor(context),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Padding(
        padding: EdgeInsets.only(
          left: 24,
          right: 24,
          top: 24,
          bottom: MediaQuery.of(context).viewInsets.bottom + 24,
        ),
        child: _isStep1 ? _buildStep1() : _buildStep2(),
      ),
    );
  }

  Widget _buildStep1() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            Icon(
              Icons.report_outlined,
              color: AppTheme.getErrorColor(context),
              size: 28,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Report ${widget.reportedUserName}',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.getTextColor(context),
                ),
              ),
            ),
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: Icon(
                Icons.close,
                color: AppTheme.getSecondaryTextColor(context),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        Text(
          'Why are you reporting this user?',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 16),

        // Reason options
        Expanded(
          child: ListView.builder(
            itemCount: _reportReasons.length,
            itemBuilder: (context, index) {
              final reason = _reportReasons[index];
              final isSelected = _selectedReason == reason;

              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                child: InkWell(
                  onTap: () {
                    setState(() {
                      _selectedReason = reason;
                    });
                  },
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isSelected
                        ? AppTheme.getPrimaryColor(context).withValues(alpha: 0.1)
                        : AppTheme.getSurfaceColor(context),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                          ? AppTheme.getPrimaryColor(context)
                          : AppTheme.getBorderColor(context),
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                          color: isSelected
                            ? AppTheme.getPrimaryColor(context)
                            : AppTheme.getSecondaryTextColor(context),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            reason,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                              color: AppTheme.getTextColor(context),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // Additional info field
        const SizedBox(height: 16),
        Text(
          'Additional information (optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _additionalInfoController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Provide more details about the issue...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppTheme.getBorderColor(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppTheme.getPrimaryColor(context), width: 2),
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Continue button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _selectedReason == null ? null : () {
              setState(() {
                _isStep1 = false;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.getPrimaryColor(context),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'Continue',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStep2() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            IconButton(
              onPressed: () {
                setState(() {
                  _isStep1 = true;
                });
              },
              icon: Icon(
                Icons.arrow_back,
                color: AppTheme.getTextColor(context),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Report ${widget.reportedUserName}',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.getTextColor(context),
                ),
              ),
            ),
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: Icon(
                Icons.close,
                color: AppTheme.getSecondaryTextColor(context),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        Text(
          'Do you want to Report and Block the user or only Report for now?',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 16),

        // Info text
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Report Only:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.getTextColor(context),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'The user will be reported to our moderation team for review.',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.getSecondaryTextColor(context),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'Report and Block:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.getTextColor(context),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'The user will be reported and blocked. You won\'t see their items or messages anymore.',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.getSecondaryTextColor(context),
                ),
              ),
            ],
          ),
        ),

        const Spacer(),

        // Action buttons
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: _isLoading ? null : () => _submitReport(false),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  side: BorderSide(color: AppTheme.getPrimaryColor(context)),
                ),
                child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(
                      'Submit Report',
                      style: TextStyle(
                        color: AppTheme.getPrimaryColor(context),
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                onPressed: _isLoading ? null : () => _submitReport(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.getErrorColor(context),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Text(
                      'Block and Report',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _submitReport(bool shouldBlock) async {
    if (_selectedReason == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (shouldBlock) {
        await ReportService.reportAndBlockUser(
          reportedUserId: widget.reportedUserId,
          reason: _selectedReason!,
          additionalInfo: _additionalInfoController.text.trim().isEmpty
            ? null
            : _additionalInfoController.text.trim(),
          chatId: widget.chatId,
        );
      } else {
        await ReportService.reportUser(
          reportedUserId: widget.reportedUserId,
          reason: _selectedReason!,
          additionalInfo: _additionalInfoController.text.trim().isEmpty
            ? null
            : _additionalInfoController.text.trim(),
          chatId: widget.chatId,
        );
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              shouldBlock
                ? 'User has been reported and blocked successfully.'
                : 'Report submitted successfully. Thank you for helping keep our community safe.',
            ),
            backgroundColor: AppTheme.getSuccessColor(context),
            duration: const Duration(seconds: 3),
          ),
        );

        // If blocked, go back to previous screen
        if (shouldBlock) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppTheme.getErrorColor(context),
          ),
        );
      }
    }
  }
}
