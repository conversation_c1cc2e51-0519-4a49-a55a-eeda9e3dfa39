import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shimmer/shimmer.dart';
import '../services/auth_service.dart';
import '../services/location_permission_service.dart';
import '../themes/app_theme.dart';
import '../providers/items_provider.dart';
import '../providers/filter_provider.dart';
import '../providers/liked_items_provider.dart';

import '../widgets/new_items_notification.dart';
import '../widgets/location_required_widget.dart';
import '../widgets/shimmer_item_card.dart';
import '../widgets/filter_row.dart';
import '../utils/size.dart';
import 'item_detail_screen.dart';
import 'liked_items_screen.dart';

class HomePageScreen extends ConsumerStatefulWidget {
  final bool showWelcomeMessage;
  final bool forceRefresh;

  const HomePageScreen({
    super.key,
    this.showWelcomeMessage = false,
    this.forceRefresh = false,
  });

  @override
  ConsumerState<HomePageScreen> createState() => _HomePageScreenState();
}

class _HomePageScreenState extends ConsumerState<HomePageScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  bool _showWelcome = false;
  bool _isFirstTimeUser = false;
  bool _hasLocationPermission = true;
  final ScrollController _scrollController = ScrollController();
  int _likedItemsCount = 0;

  // Animation controllers for heart flip
  late AnimationController _flipController;
  late Animation<double> _flipAnimation;
  bool _showCount = false;
  Timer? _flipTimer;

  @override
  void initState() {
    super.initState();

    // Initialize flip animation
    _flipController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _flipAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _flipController,
      curve: Curves.easeInOut,
    ));

    if (widget.showWelcomeMessage) {
      _initializeWelcomeMessage();
    }
    _checkLocationPermission();
    _loadLikedItemsCount();

    // Add observer for app lifecycle
    WidgetsBinding.instance.addObserver(this);

    // Force refresh if requested (for returning users after login)
    if (widget.forceRefresh) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(itemsProvider.notifier).forceRefreshOnLogin();
      });
    }
  }

  Future<void> _loadLikedItemsCount() async {
    try {
      final currentUser = AuthService.currentUser;
      if (currentUser != null) {
        final querySnapshot = await FirebaseFirestore.instance
            .collection('users')
            .doc(currentUser.uid)
            .collection('likedItems')
            .get();

        if (mounted) {
          final newCount = querySnapshot.docs.length;
          setState(() {
            _likedItemsCount = newCount;
          });

          // Start or stop animation based on count
          if (newCount > 0) {
            _startFlipAnimation();
          } else {
            _stopFlipAnimation();
          }
        }
      }
    } catch (e) {
      // Silently handle error - not critical for UI
      debugPrint('Error loading liked items count: $e');
    }
  }

  void _startFlipAnimation() {
    _stopFlipAnimation(); // Stop any existing timer

    _flipTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      final currentCount = ref.read(likedItemsProvider).count;
      if (mounted && currentCount > 0) {
        setState(() {
          _showCount = !_showCount;
        });

        if (_showCount) {
          _flipController.forward();
        } else {
          _flipController.reverse();
        }
      } else {
        timer.cancel();
      }
    });
  }

  void _stopFlipAnimation({bool skipSetState = false}) {
    _flipTimer?.cancel();
    _flipTimer = null;
    if (mounted && !skipSetState) {
      setState(() {
        _showCount = false;
      });
    }
    _flipController.reset();
  }

  Future<void> _checkLocationPermission() async {
    final hasPermission = await LocationPermissionService.isLocationPermissionGranted();
    if (mounted) {
      setState(() {
        _hasLocationPermission = hasPermission;
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _stopFlipAnimation(skipSetState: true);
    _flipController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Pause animation when app goes to background, resume when it comes back
    if (state == AppLifecycleState.paused || state == AppLifecycleState.inactive) {
      _stopFlipAnimation();
    } else if (state == AppLifecycleState.resumed && _likedItemsCount > 0) {
      _startFlipAnimation();
    }
  }

  Future<void> _initializeWelcomeMessage() async {
    // Check if welcome message was already shown in this session
    final prefs = await SharedPreferences.getInstance();
    final user = AuthService.currentUser;
    final welcomeKey = 'welcome_shown_${user?.uid ?? 'unknown'}';
    final alreadyShown = prefs.getBool(welcomeKey) ?? false;

    if (alreadyShown) {
      return; // Don't show welcome message again
    }

    if (user?.uid != null) {
      try {
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user!.uid)
            .get();

        if (userDoc.exists) {
          final data = userDoc.data();
          final createdAt = data?['createdAt'] as Timestamp?;

          if (createdAt != null) {
            final now = DateTime.now();
            final accountAge = now.difference(createdAt.toDate());
            _isFirstTimeUser = accountAge.inDays < 1;
          }
        }

        if (mounted) {
          setState(() {
            _showWelcome = true;
          });

          // Mark welcome message as shown
          await prefs.setBool(welcomeKey, true);

          // Auto-hide welcome message after 3 seconds
          Future.delayed(const Duration(seconds: 3), () {
            if (mounted) {
              setState(() {
                _showWelcome = false;
              });
            }
          });
        }
      } catch (e) {
        // Handle error silently
      }
    }
  }

  /// Pull-to-refresh functionality
  Future<void> _onRefresh() async {
    await ref.read(itemsProvider.notifier).refreshItems();
  }

  @override
  Widget build(BuildContext context) {
    final itemsState = ref.watch(itemsProvider);
    final filteredItems = ref.watch(filteredItemsProvider);
    final filterState = ref.watch(filterProvider);
    final likedItemsState = ref.watch(likedItemsProvider);
    final currentLocation = itemsState.lastLocation;

    // Listen to liked items changes to trigger animation
    ref.listen<LikedItemsState>(likedItemsProvider, (previous, next) {
      if (previous?.count != next.count) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            if (next.count > 0) {
              _startFlipAnimation();
            } else {
              _stopFlipAnimation();
            }
          }
        });
      }
    });

    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        backgroundColor: AppTheme.getBackgroundColor(context),
        elevation: 0,
        title: Text(
          'EvenOut',
          style: TextStyle(
            color: AppTheme.getTextColor(context),
            fontWeight: FontWeight.bold,
            fontSize: MySize.size24,
          ),
        ),
        actions: [
          // Heart/Liked items button with flip animation
          likedItemsState.count == 0
              ? IconButton(
                  onPressed: () async {
                    await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const LikedItemsScreen(),
                      ),
                    );
                    // Refresh count when returning from liked items screen
                    ref.read(likedItemsProvider.notifier).refresh();
                  },
                  icon: Icon(
                    Icons.favorite,
                    color: Colors.red,
                    size: MySize.size24,
                  ),
                )
              : GestureDetector(
                  onTap: () async {
                    await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const LikedItemsScreen(),
                      ),
                    );
                    // Refresh count when returning from liked items screen
                    ref.read(likedItemsProvider.notifier).refresh();
                  },
                  child: Container(
                    width: MySize.size48,
                    height: MySize.size48,
                    alignment: Alignment.center,
                    child: AnimatedBuilder(
                      animation: _flipAnimation,
                      builder: (context, child) {
                        // Determine which widget to show based on animation progress
                        final isShowingFront = _flipAnimation.value < 0.5;

                        return Transform(
                          alignment: Alignment.center,
                          transform: Matrix4.identity()
                            ..setEntry(3, 2, 0.001)
                            ..rotateX(_flipAnimation.value * 3.14159),
                          child: isShowingFront
                              ? Icon(
                                  Icons.favorite,
                                  color: Colors.red,
                                  size: MySize.size24,
                                )
                              : Transform(
                                  alignment: Alignment.center,
                                  transform: Matrix4.identity()..rotateX(3.14159),
                                  child: Container(
                                    width: MySize.size28,
                                    height: MySize.size28,
                                    decoration: BoxDecoration(
                                      color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.1),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: Text(
                                        likedItemsState.count > 99 ? '99+' : likedItemsState.count.toString(),
                                        style: TextStyle(
                                          color: AppTheme.getPrimaryColor(context),
                                          fontSize: MySize.size12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                                ),
                        );
                      },
                    ),
                  ),
                ),

          SizedBox(width: MySize.size2),

          // Location pill
          Container(
            padding: EdgeInsets.symmetric(horizontal: MySize.size12, vertical: MySize.size6),
            decoration: BoxDecoration(
              color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(MySize.size16),
            ),
            child: currentLocation?.city != null
                ? Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.location_on,
                        size: MySize.size16,
                        color: AppTheme.getPrimaryColor(context),
                      ),
                      SizedBox(width: MySize.size4),
                      Text(
                        currentLocation!.city!,
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppTheme.getPrimaryColor(context),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  )
                : const ShimmerLocationHeader(),
          ),
          SizedBox(width: MySize.size16),
        ],
      ),
      body: ItemsNotificationOverlay(
        scrollController: _scrollController,
        onNewItemsTap: () {
          // Additional callback if needed
        },
        child: Column(
          children: [
            // Welcome message
            if (_showWelcome)
              Container(
                width: double.infinity,
                margin: EdgeInsets.all(MySize.size16),
                padding: EdgeInsets.all(MySize.size16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppTheme.getPrimaryColor(context),
                      AppTheme.getPrimaryColor(context).withValues(alpha: 0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(MySize.size16),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.3),
                      blurRadius: MySize.size10,
                      offset: Offset(0, MySize.size4),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(MySize.size12),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(MySize.size12),
                      ),
                      child: Icon(
                        Icons.waving_hand,
                        color: Colors.white,
                        size: MySize.size24,
                      ),
                    ),
                    SizedBox(width: MySize.size16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _isFirstTimeUser ? 'Welcome!' : 'Welcome Back!',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: MySize.size18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: MySize.size4),
                          Text(
                            _isFirstTimeUser
                                ? 'Discover amazing items near you'
                                : 'Check out what\'s new in your area',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.9),
                              fontSize: MySize.size14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

            // Filter row
            const FilterRow(),

            // Items grid
            Expanded(
              child: _buildItemsContent(itemsState, filteredItems, filterState),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsContent(ItemsState itemsState, List<Map<String, dynamic>> filteredItems, FilterState filterState) {
    // Show location required widget if permission not granted
    if (!_hasLocationPermission) {
      return SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: MySize.size20),
            LocationRequiredWidget(
              onLocationGranted: () {
                _checkLocationPermission();
                // Refresh items after permission granted
                ref.read(itemsProvider.notifier).forceRefreshOnLogin();
              },
            ),
          ],
        ),
      );
    }

    // Show shimmer loading if no initial data or location is being fetched
    if (itemsState.isLoading && filteredItems.isEmpty) {
      return const ShimmerItemGridWithHeader(
        itemCount: 6,
      );
    }

    // Show shimmer loading when filtering
    if (filterState.isFiltering) {
      return const ShimmerItemGridWithHeader(
        itemCount: 6,
      );
    }

    // Show empty state
    if (filteredItems.isEmpty) {
      return RefreshIndicator(
        onRefresh: _onRefresh,
        color: AppTheme.getPrimaryColor(context),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: MediaQuery.of(context).size.height,
            child: Center(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.getSurfaceColor(context),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.inventory_2_outlined,
                      size: MySize.size64,
                      color: AppTheme.getSecondaryTextColor(context),
                    ),
                    SizedBox(height: MySize.size16),
                    Text(
                      'No Items Available',
                      style: TextStyle(
                        fontSize: MySize.size24,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.getTextColor(context),
                      ),
                    ),
                    SizedBox(height: MySize.size8),
                    Text(
                      'Be the first to list an item in your area!',
                      style: TextStyle(
                        fontSize: MySize.size16,
                        color: AppTheme.getSecondaryTextColor(context),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    }

    // Show items in grid
    return RefreshIndicator(
      onRefresh: _onRefresh,
      color: AppTheme.getPrimaryColor(context),
      child: GridView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(MySize.size16),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: MySize.size16,
          mainAxisSpacing: MySize.size16,
          childAspectRatio: 0.75,
        ),
        itemCount: filteredItems.length,
        itemBuilder: (context, index) {
          final item = filteredItems[index];
          return _buildGridItemCard(item);
        },
      ),
    );
  }

  Widget _buildGridItemCard(Map<String, dynamic> item) {
    final images = item['images'] as List<dynamic>? ?? [];
    final imageUrl = images.isNotEmpty ? images[0] as String : null;
    final distance = item['distance'] as double;
    final price = item['price'] as double?;
    final heroTag = 'item_${item['docId']}_${DateTime.now().millisecondsSinceEpoch}';

    return Container(
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              PageRouteBuilder(
                pageBuilder: (context, animation, secondaryAnimation) => ItemDetailScreen(
                  itemId: item['docId'],
                  itemData: item,
                  heroTag: heroTag,
                ),
                transitionDuration: const Duration(milliseconds: 300),
                reverseTransitionDuration: const Duration(milliseconds: 300),
                transitionsBuilder: (context, animation, secondaryAnimation, child) {
                  const curve = Curves.easeInOut;

                  var fadeAnimation = Tween(begin: 0.0, end: 1.0).animate(
                    CurvedAnimation(parent: animation, curve: curve),
                  );

                  return FadeTransition(
                    opacity: fadeAnimation,
                    child: child,
                  );
                },
              ),
            );
          },
          borderRadius: BorderRadius.circular(MySize.size16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Item Image with Hero
              Expanded(
                flex: 3,
                child: Hero(
                  tag: heroTag,
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.vertical(top: Radius.circular(MySize.size16)),
                      color: AppTheme.getBackgroundColor(context),
                    ),
                    child: imageUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.vertical(top: Radius.circular(MySize.size16)),
                            child: Image.network(
                              imageUrl,
                              fit: BoxFit.cover,
                              loadingBuilder: (context, child, loadingProgress) {
                                if (loadingProgress == null) return child;
                                return Shimmer.fromColors(
                                  baseColor: AppTheme.getBackgroundColor(context),
                                  highlightColor: AppTheme.getSurfaceColor(context),
                                  child: Container(
                                    width: double.infinity,
                                    height: double.infinity,
                                    color: AppTheme.getBackgroundColor(context),
                                  ),
                                );
                              },
                              errorBuilder: (context, error, stackTrace) {
                                return Center(
                                  child: Icon(
                                    Icons.image_not_supported,
                                    size: MySize.size32,
                                    color: AppTheme.getSecondaryTextColor(context),
                                  ),
                                );
                              },
                            ),
                          )
                        : Center(
                            child: Icon(
                              Icons.image,
                              size: MySize.size32,
                              color: AppTheme.getSecondaryTextColor(context),
                            ),
                          ),
                  ),
                ),
              ),

              // Item Details
              Expanded(
                flex: 2,
                child: Padding(
                  padding: EdgeInsets.all(MySize.size12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Item Name
                      Text(
                        item['itemName'] ?? 'Unknown Item',
                        style: TextStyle(
                          fontSize: MySize.size14,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.getTextColor(context),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      SizedBox(height: MySize.size4),

                      // Price
                      if (price != null)
                        Text(
                          '₹${price.toStringAsFixed(0)}',
                          style: TextStyle(
                            fontSize: MySize.size16,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.getPrimaryColor(context),
                          ),
                        )
                      else
                        Text(
                          'Free',
                          style: TextStyle(
                            fontSize: MySize.size16,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.getPrimaryColor(context),
                          ),
                        ),

                      const Spacer(),

                      // Distance and Category
                      Row(
                        children: [
                          if (distance != double.infinity) ...[
                            Expanded(
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: MySize.size6, vertical: MySize.size2),
                                decoration: BoxDecoration(
                                  color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(MySize.size6),
                                ),
                                child: Text(
                                  distance < 1
                                      ? '${(distance * 1000).round()}m'
                                      : '${distance.toStringAsFixed(1)}km',
                                  style: TextStyle(
                                    fontSize: MySize.size10,
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.getPrimaryColor(context),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ] else ...[
                            Expanded(
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: MySize.size6, vertical: MySize.size2),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFE6C068).withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(MySize.size6),
                                ),
                                child: Text(
                                  item['category'] ?? 'Other',
                                  style: TextStyle(
                                    fontSize: MySize.size10,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFFE6C068),
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}