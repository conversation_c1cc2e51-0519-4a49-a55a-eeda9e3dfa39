import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class ReportService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Report a user
  static Future<void> reportUser({
    required String reportedUserId,
    required String reason,
    String? additionalInfo,
    required String chatId,
  }) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) throw 'User not authenticated';

    try {
      await _firestore.collection('reports').add({
        'reporterId': currentUser.uid,
        'reportedUserId': reportedUserId,
        'reason': reason,
        'additionalInfo': additionalInfo,
        'chatId': chatId,
        'timestamp': FieldValue.serverTimestamp(),
        'status': 'pending',
        'type': 'user_report',
      });
    } catch (e) {
      throw 'Failed to submit report: $e';
    }
  }

  /// Block a user
  static Future<void> blockUser({
    required String blockedUserId,
    required String chatId,
  }) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) throw 'User not authenticated';

    try {
      // Add to blocked users collection
      await _firestore.collection('blocked_users').add({
        'blockerId': currentUser.uid,
        'blockedUserId': blockedUserId,
        'timestamp': FieldValue.serverTimestamp(),
        'chatId': chatId,
      });

      // Update user's blocked list
      await _firestore.collection('users').doc(currentUser.uid).update({
        'blockedUsers': FieldValue.arrayUnion([blockedUserId]),
      });

      // Update the other user's blocked by list
      await _firestore.collection('users').doc(blockedUserId).update({
        'blockedBy': FieldValue.arrayUnion([currentUser.uid]),
      });

      // Close the chat
      await _firestore.collection('chats').doc(chatId).update({
        'isBlocked': true,
        'blockedBy': currentUser.uid,
        'blockedAt': FieldValue.serverTimestamp(),
        'status': 'blocked',
      });

    } catch (e) {
      throw 'Failed to block user: $e';
    }
  }

  /// Report and block a user
  static Future<void> reportAndBlockUser({
    required String reportedUserId,
    required String reason,
    String? additionalInfo,
    required String chatId,
  }) async {
    try {
      // First report the user
      await reportUser(
        reportedUserId: reportedUserId,
        reason: reason,
        additionalInfo: additionalInfo,
        chatId: chatId,
      );

      // Then block the user
      await blockUser(
        blockedUserId: reportedUserId,
        chatId: chatId,
      );
    } catch (e) {
      throw 'Failed to report and block user: $e';
    }
  }

  /// Check if a user is blocked
  static Future<bool> isUserBlocked(String userId) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return false;

    try {
      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      final blockedUsers = List<String>.from(userDoc.data()?['blockedUsers'] ?? []);
      return blockedUsers.contains(userId);
    } catch (e) {
      return false;
    }
  }

  /// Get blocked users list
  static Future<List<String>> getBlockedUsers() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return [];

    try {
      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      return List<String>.from(userDoc.data()?['blockedUsers'] ?? []);
    } catch (e) {
      return [];
    }
  }
}
