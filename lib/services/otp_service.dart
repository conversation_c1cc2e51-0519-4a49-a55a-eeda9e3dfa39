import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'dart:math';

class OTPService {
  static final FirebaseFunctions _functions = FirebaseFunctions.instance;

  // Placeholder for OTP storage (used when cloud functions are not available)
  static final Map<String, Map<String, dynamic>> _otpStorage = {};

  // Flag to determine if cloud functions are available
  static bool _useCloudFunctions = false;

  /// Check if cloud functions are available and configure accordingly
  static Future<void> initialize() async {
    try {
      // Try to call a test function to see if cloud functions are deployed
      final callable = _functions.httpsCallable('testConnection');
      await callable.call().timeout(const Duration(seconds: 3));
      _useCloudFunctions = true;
      print('Cloud Functions are available - using production email service');
    } catch (e) {
      _useCloudFunctions = false;
      print('Cloud Functions not available - using local OTP simulation');
    }
  }

  /// Generates a 6-digit OTP
  static String _generateOTP() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString();
  }

  /// Validates OTP format (6 digits)
  static bool isValidOTP(String otp) {
    if (otp.length != 6) return false;
    return RegExp(r'^\d{6}$').hasMatch(otp);
  }

  /// Sends OTP to the specified email address
  ///
  /// [email] - The email address to send OTP to
  /// [userName] - Optional user name for personalization
  ///
  /// Returns a Map with success status, message, and expiration time
  ///
  /// Throws an exception if sending fails
  static Future<Map<String, dynamic>> sendEmailOTP({
    required String email,
    String? userName,
  }) async {
    try {
      if (_useCloudFunctions) {
        // Use cloud functions for production email sending
        try {
          return await _sendEmailOTPViaCloudFunctions(email: email, userName: userName);
        } catch (cloudError) {
          print('Cloud Functions failed, falling back to local simulation: $cloudError');
          // Fallback to local simulation if cloud functions fail
          return await _sendEmailOTPLocally(email: email, userName: userName);
        }
      } else {
        // Use local simulation for development/testing
        return await _sendEmailOTPLocally(email: email, userName: userName);
      }
    } catch (e) {
      throw Exception('Failed to send OTP: ${e.toString()}');
    }
  }

  /// Send OTP via Cloud Functions (production)
  static Future<Map<String, dynamic>> _sendEmailOTPViaCloudFunctions({
    required String email,
    String? userName,
  }) async {
    print('Attempting to send OTP via Cloud Functions to: $email');

    final callable = _functions.httpsCallable('sendOTPEmail');

    final requestData = {
      'email': email.toLowerCase().trim(),
      'userName': userName ?? 'User',
    };

    print('Calling Cloud Function with data: $requestData');
    final result = await callable.call(requestData);
    print('Cloud Function response: ${result.data}');

    if (result.data['success'] == true) {
      print('Cloud Function succeeded - email sent successfully');
      return {
        'success': true,
        'message': result.data['message'] ?? 'OTP sent successfully',
        'expiresAt': result.data['expiresAt'],
      };
    } else {
      throw Exception(result.data['message'] ?? 'Failed to send OTP');
    }
  }

  /// Send Account Deletion OTP via Cloud Functions (production)
  static Future<Map<String, dynamic>> _sendAccountDeletionOTPViaCloudFunctions({
    required String email,
    String? userName,
  }) async {
    print('Attempting to send Account Deletion OTP via Cloud Functions to: $email');

    final callable = _functions.httpsCallable('sendAccountDeletionOTP');

    final requestData = {
      'email': email.toLowerCase().trim(),
      'userName': userName ?? 'User',
    };

    print('Calling Account Deletion OTP Cloud Function with data: $requestData');
    final result = await callable.call(requestData);
    print('Account Deletion OTP Cloud Function response: ${result.data}');

    if (result.data['success'] == true) {
      print('Account Deletion OTP Cloud Function succeeded - email sent successfully');
      return {
        'success': true,
        'message': result.data['message'] ?? 'Account deletion OTP sent successfully',
        'expiresAt': result.data['expiresAt'],
      };
    } else {
      throw Exception(result.data['message'] ?? 'Failed to send account deletion OTP');
    }
  }

  /// Send OTP locally (development/testing)
  static Future<Map<String, dynamic>> _sendEmailOTPLocally({
    required String email,
    String? userName,
  }) async {
    // Generate a 6-digit OTP
    final otp = _generateOTP();
    final expiresAt = DateTime.now().add(const Duration(minutes: 10));

    // Store OTP temporarily (in production, use secure backend)
    _otpStorage[email.toLowerCase().trim()] = {
      'otp': otp,
      'expiresAt': expiresAt.millisecondsSinceEpoch,
      'userName': userName ?? 'User',
    };

    // In production, send actual email here
    // For now, we'll simulate success and log the OTP for testing
    print('OTP for $email: $otp (expires at $expiresAt)');

    return {
      'success': true,
      'message': 'OTP sent successfully to $email',
      'expiresAt': expiresAt.millisecondsSinceEpoch,
      'testOTP': otp, // Remove this in production
    };
  }

  /// Verify Account Deletion OTP via Cloud Functions (production)
  static Future<Map<String, dynamic>> _verifyAccountDeletionOTPViaCloudFunctions({
    required String email,
    required String otp,
  }) async {
    print('Attempting to verify Account Deletion OTP via Cloud Functions for: $email');

    final callable = _functions.httpsCallable('verifyAccountDeletionOTP');

    final requestData = {
      'email': email.toLowerCase().trim(),
      'otp': otp,
    };

    print('Calling Account Deletion OTP verification Cloud Function with data: $requestData');
    final result = await callable.call(requestData);
    print('Account Deletion OTP verification Cloud Function response: ${result.data}');

    if (result.data['success'] == true) {
      print('Account Deletion OTP verification succeeded');
      return {
        'success': true,
        'message': result.data['message'] ?? 'Account deletion OTP verified successfully',
      };
    } else {
      throw Exception(result.data['message'] ?? 'Failed to verify account deletion OTP');
    }
  }

  /// Verifies the OTP for the specified email
  ///
  /// [email] - The email address to verify OTP for
  /// [otp] - The OTP to verify
  ///
  /// Returns a Map with success status and message
  ///
  /// Throws an exception if verification fails
  static Future<Map<String, dynamic>> verifyEmailOTP({
    required String email,
    required String otp,
  }) async {
    try {
      if (_useCloudFunctions) {
        // Use cloud functions for production OTP verification
        return await _verifyEmailOTPViaCloudFunctions(email: email, otp: otp);
      } else {
        // Use local verification for development/testing
        return await _verifyEmailOTPLocally(email: email, otp: otp);
      }
    } catch (e) {
      throw Exception('Failed to verify OTP: ${e.toString()}');
    }
  }

  /// Verify OTP via Cloud Functions (production)
  static Future<Map<String, dynamic>> _verifyEmailOTPViaCloudFunctions({
    required String email,
    required String otp,
  }) async {
    final callable = _functions.httpsCallable('verifyOTP');

    final result = await callable.call({
      'email': email.toLowerCase().trim(),
      'otp': otp.trim(),
    });

    if (result.data['success'] == true) {
      return {
        'success': true,
        'message': result.data['message'] ?? 'OTP verified successfully',
      };
    } else {
      throw Exception(result.data['message'] ?? 'Failed to verify OTP');
    }
  }

  /// Verify OTP locally (development/testing)
  static Future<Map<String, dynamic>> _verifyEmailOTPLocally({
    required String email,
    required String otp,
  }) async {
    final emailKey = email.toLowerCase().trim();
    final storedData = _otpStorage[emailKey];

    if (storedData == null) {
      throw Exception('No OTP found for this email. Please request a new OTP.');
    }

    final storedOTP = storedData['otp'] as String;
    final expiresAt = storedData['expiresAt'] as int;
    final now = DateTime.now().millisecondsSinceEpoch;

    if (now > expiresAt) {
      _otpStorage.remove(emailKey);
      throw Exception('OTP has expired. Please request a new OTP.');
    }

    if (otp.trim() != storedOTP) {
      throw Exception('Invalid OTP. Please check and try again.');
    }

    // OTP is valid, remove it from storage
    _otpStorage.remove(emailKey);

    return {
      'success': true,
      'message': 'OTP verified successfully',
    };
  }

  /// Send Welcome Email via Cloud Functions (production)
  static Future<Map<String, dynamic>> _sendWelcomeEmailViaCloudFunctions({
    required String email,
    String? userName,
  }) async {
    print('Attempting to send Welcome Email via Cloud Functions to: $email');

    final callable = _functions.httpsCallable('sendWelcomeEmail');

    final requestData = {
      'email': email.toLowerCase().trim(),
      'userName': userName ?? 'User',
    };

    print('Calling Welcome Email Cloud Function with data: $requestData');
    final result = await callable.call(requestData);
    print('Welcome Email Cloud Function response: ${result.data}');

    if (result.data['success'] == true) {
      print('Welcome Email Cloud Function succeeded - email sent successfully');
      return {
        'success': true,
        'message': result.data['message'] ?? 'Welcome email sent successfully',
      };
    } else {
      throw Exception(result.data['message'] ?? 'Failed to send welcome email');
    }
  }

  // Placeholder methods for other email functionalities
  // In production, these would use a proper email service

  static Future<Map<String, dynamic>> sendWelcomeEmail({
    required String email,
    String? userName,
  }) async {
    try {
      if (_useCloudFunctions) {
        // Use cloud functions for production welcome email
        try {
          return await _sendWelcomeEmailViaCloudFunctions(email: email, userName: userName);
        } catch (cloudError) {
          print('Cloud Functions failed for welcome email, falling back to local simulation: $cloudError');
          // Fallback to local simulation if cloud functions fail
          print('Welcome email would be sent to $email');
          return {'success': true, 'message': 'Welcome email sent'};
        }
      } else {
        // Use local simulation for development/testing
        print('Welcome email would be sent to $email');
        return {'success': true, 'message': 'Welcome email sent'};
      }
    } catch (e) {
      throw Exception('Failed to send welcome email: ${e.toString()}');
    }
  }

  static Future<Map<String, dynamic>> sendEmailVerifiedNotification({
    required String email,
    String? userName,
  }) async {
    print('Email verified notification would be sent to $email');
    return {'success': true, 'message': 'Email verified notification sent'};
  }

  static Future<Map<String, dynamic>> sendDocumentSubmittedNotification({
    required String email,
    String? userName,
    String? documentType,
  }) async {
    print('Document submitted notification would be sent to $email');
    return {'success': true, 'message': 'Document submitted notification sent'};
  }

  static Future<Map<String, dynamic>> sendDocumentVerifiedNotification({
    required String email,
    String? userName,
    String? documentType,
  }) async {
    print('Document verified notification would be sent to $email');
    return {'success': true, 'message': 'Document verified notification sent'};
  }

  static Future<Map<String, dynamic>> sendAccountDeletionOTP({
    required String email,
    String? userName,
  }) async {
    try {
      if (_useCloudFunctions) {
        // Use cloud functions for production account deletion OTP
        try {
          return await _sendAccountDeletionOTPViaCloudFunctions(email: email, userName: userName);
        } catch (cloudError) {
          print('Cloud Functions failed for account deletion OTP, falling back to local simulation: $cloudError');
          // Fallback to local simulation if cloud functions fail
          return await _sendEmailOTPLocally(email: email, userName: userName);
        }
      } else {
        // Use local simulation for development/testing
        return await _sendEmailOTPLocally(email: email, userName: userName);
      }
    } catch (e) {
      throw Exception('Failed to send account deletion OTP: ${e.toString()}');
    }
  }

  static Future<Map<String, dynamic>> verifyAccountDeletionOTP({
    required String email,
    required String otp,
  }) async {
    try {
      if (_useCloudFunctions) {
        // Use cloud functions for production account deletion OTP verification
        try {
          return await _verifyAccountDeletionOTPViaCloudFunctions(email: email, otp: otp);
        } catch (cloudError) {
          print('Cloud Functions failed for account deletion OTP verification, falling back to local simulation: $cloudError');
          // Fallback to local verification if cloud functions fail
          return await _verifyEmailOTPLocally(email: email, otp: otp);
        }
      } else {
        // Use local verification for development/testing
        return await _verifyEmailOTPLocally(email: email, otp: otp);
      }
    } catch (e) {
      throw Exception('Failed to verify account deletion OTP: ${e.toString()}');
    }
  }

  static Future<Map<String, dynamic>> sendSwapAcceptedEmails({
    required String ownerEmail,
    required String requesterEmail,
    String? ownerName,
    String? requesterName,
    String? ownerItemName,
    String? requesterItemName,
  }) async {
    print('Swap accepted emails would be sent to $ownerEmail and $requesterEmail');
    return {'success': true, 'message': 'Swap accepted emails sent'};
  }

  static Future<Map<String, dynamic>> sendSwapRejectedEmails({
    required String ownerEmail,
    required String requesterEmail,
    String? ownerName,
    String? requesterName,
    required String itemName,
  }) async {
    print('Swap rejected emails would be sent to $ownerEmail and $requesterEmail');
    return {'success': true, 'message': 'Swap rejected emails sent'};
  }
}
